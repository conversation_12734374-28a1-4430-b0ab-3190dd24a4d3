import React from 'react';
import AiChatbotIcon from '../Icons/AiChatbotIcon';

const AnimatedChatButton = ({ onClick, isOpen }) => {
  return (
    <div className="fixed bottom-6 right-6 z-50 flex items-center justify-center">
      <div className="relative flex items-center justify-center">
        <button
          onClick={onClick}
          className="relative bg-white shadow-lg rounded-full flex items-center px-4 py-2 border border-grey-10 min-w-[120px] min-h-[48px] transition-all duration-200 hover:shadow-xl overflow-hidden"
        >
          <AiChatbotIcon className="w-4 h-4 text-purple-300 mr-2 mt-0.5" />
          <span className="font-medium text-grey-75 text-sm">Chat with us</span>
        </button>
      </div>
    </div>
  );
};

export default AnimatedChatButton; 