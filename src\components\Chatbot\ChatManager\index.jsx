import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';

import { DANTE_ICON, DEFAULT_CHAT_MESSAGES, LLM_MODEL_DEFAULT, STATUS } from '@/constants';
import formatEventData from '@/helpers/formatEventData';
import transformMessageToBubbleMessage from '@/helpers/transformMessageToBubbleMessage';
import useTaskHandler from '@/hooks/useTaskHandler';
import {
  sendMessageEmbed,
  sendMessageInApp,
  createSharedFormData,
  requestTakeover,
  pollTakeover,
  showDynamicButtons,
} from '@/services/chat.service';
import * as conversationsService from '@/services/conversations.service';
import * as customizationService from '@/services/customization.service';
import * as messagesService from '@/services/message.service';
import * as suggestionService from '@/services/suggestion.service';
import * as userService from '@/services/user.service';
import { useConversationStore } from '@/stores/conversation/conversationStore';
import useTaskStore from '@/stores/task/taskStore';

import Chat from '../Chat';
import { useChatbotStore } from '@/stores/chatbot/chatbotStore';
import { useSearchParams } from 'react-router-dom';
import DOMPurify from 'dompurify';
import { DateTime } from 'luxon';
import * as ttsService from '@/services/tts.service';
import { usePreviousConversationsStore } from '@/stores/previousConversation/previousConversationStore';
import PropTypes from 'prop-types';
import { useUserStore } from '@/stores/user/userStore';
import * as modelService from '@/services/model.service';
import * as humanHandoverService from '@/services/humanHandover';
import transformMessageToBubbleMessageHumanHandover from '@/helpers/transformMessageToBubbleMessageHumanHandover';
import { useHumanHandoverStore } from '@/stores/humanHandover/humanHandoverStore';
import { showNotificationWithSound, unlockAudioContext } from '@/helpers/notificationHelper';
import DSpinner from '@/components/Global/DSpinner';
import useHumanHandoverWebSocket from '@/hooks/useHumanHandoverWebSocket';
import VoicePreview from '@/components/Voice/VoicePreview';
import { WS_ENDPOINT_TYPES } from '@/services/websocket/voiceWebSocketUtils';
import { useTimezoneStore } from '@/stores/timezone/timezoneStore';

class RetriableError extends Error {}
class FatalError extends Error {}

const ChatManager = ({
  config,
  hiddenConversation = false,
  isInApp = false,
  isDanteFaq = false,
  showMenuBtn = true,
  showCloseBtn = false,
  handleOpenDanteConversations,
  handleOpenDanteFaq,
  handleCloseButton,
  showInAppHeader = true,
  hiddenLlmSelector = false,
  initialShouldFetchCustomization = true,
  isPreviewMode = false,
  cookie,
  setCookie,
  forceRefetch,
  promptToSend,
  onPromptProcessed,
  isInHumanHandoverApp = false,
  isLiveAgentConversationResolved = false,
  liveAgentConversation = null,
  webSocketHook = null,
  showEmailTranscript = false,
  setShowEmailTranscript = () => {},
}) => {
  const [urlParams] = useSearchParams();
  const currentConversation = useConversationStore(
    (state) => state.currentConversation
  );
  const setCurrentConversation = useConversationStore(
    (state) => state.setCurrentConversation
  );
  const resetCurrentConversation = useConversationStore(
    (state) => state.resetCurrentConversation
  );

  const { addOpenedConversation, setLiveAgentConversationEvent, getLiveAgentConversationEvents, deleteLiveAgentConversationEvent } = useHumanHandoverStore((state) => state);

  const selectedChatbot = useChatbotStore((state) => state.selectedChatbot);
  const setUserData = useUserStore((state) => state.setUser);
  const userData = useUserStore((state) => state.user);
  const auth = useUserStore((state) => state.auth);
  const setSaveSignUpBtn = useChatbotStore((state) => state.setSaveSignUpBtn);

  const [customizationData, setCustomizationData] = useState(config);

  const [messages, setMessages] = useState(
    config?.messages
      ? [...(config.messages || []), ...(config.initial_messages || [])]
      : config?.initial_messages || []
  );

  const [suggestionPrompts, setSuggestionPrompts] = useState(
    config?.prompt_suggestions
  );
  const [showSuggestionPrompts, setShowSuggestionPrompts] = useState(true);

  const [control, setControl] = useState(null);
  const [shouldFetchCustomization, setShouldFetchCustomization] = useState(
    initialShouldFetchCustomization
  );
  const [isAnswerLoading, setIsAnswerLoading] = useState(false);
  //show buttons in chat (suggested prompts, live agent, etc)
  const [showButtons, setShowButtons] = useState(true);
  const [interactingWithLiveAgent, setInteractingWithLiveAgent] =
    useState(false);
  const [userName, setUserName] = useState('');
  const [userEmail, setUserEmail] = useState('');
  const [pollResponse, setPollResponse] = useState(false);
  const [isLiveAgentResolved, setIsLiveAgentResolved] = useState(isLiveAgentConversationResolved);
  const [agentImage, setAgentImage] = useState(null);
  const [sendEventConnected, setSendEventConnected] = useState(null);
  const chatContainerRef = useRef(null);
  const queryStreamOpen = useRef(false);
  const wsRef = useRef(null);
  const liveAgentData = useHumanHandoverStore((state) => state.userOrganization);

  const timezone = useTimezoneStore((state) => state.timezone);


  // Always initialize the WebSocket hook to maintain consistent hook order
  const internalWebSocketHook = useHumanHandoverWebSocket({
    token: config?.token,
    isInApp,
    isInHumanHandoverApp,
    wsRef,
    onMessageReceived: (parsedData) => {
      // Handle incoming message
      const newMessage = {
        role: isInHumanHandoverApp ? 'user' : 'assistant',
        content: parsedData.data.text,
        type: 'message',
        agent_name:  liveAgentData?.name || parsedData.data.agent_name,
        agent_profile_pic: liveAgentData?.image || parsedData.data.agent_profile_pic,
        images: parsedData.data.images
      };

      handleMessagesOnChat('add', newMessage);

      if (isInHumanHandoverApp) {
        showNotificationWithSound('New Message', {
          body: 'User said: ' + parsedData.data.text,
          icon: DANTE_ICON,
        });
      }
    },
    onAgentJoined: ({ infoAgent }) => {
      // Agent has joined the conversation
      const newMessage = {
        type: 'info',
        content: `Agent ${infoAgent?.agent_name ? infoAgent?.agent_name : ''} has joined the conversation and is ready to assist you`,
      };
      handleMessagesOnChat('add', newMessage);
    },
    onAgentResolved: ({ infoAgent }) => {
      // Agent has resolved the conversation
      const newMessage = {
        type: 'info',
        content: `Conversation has been marked as resolved by agent ${infoAgent?.agent_name ? infoAgent?.agent_name : ''}. Thank you for using our service.`,
      };
      handleMessagesOnChat('add', newMessage);
      setPollResponse(false);
      setIsLiveAgentResolved(true);
    }
  });

  // Use the provided webSocketHook or the internal one
  const {
    connect: connectWebSocket,
    disconnect: disconnectWebSocket,
    sendMessage: sendWebSocketMessage,
    sendControlMessage: sendWebSocketControlMessage,
    isConnected: isWebSocketConnected,
  } = webSocketHook || internalWebSocketHook;
  const [notification, setNotification] = useState({
    show: false,
    type: '',
    message: '',
  });
  const [questionIsQuickResponse, setQuestionIsQuickResponse] = useState(false);
  const [sourcesReady, setSourcesReady] = useState(false);
  const [sourcesLoading, setSourcesLoading] = useState([]);
  const [onGoingStream, setOnGoingStream] = useState(false);
  const [messageLoaded, setMessageLoaded] = useState(false);
  const [speechPlaying, setSpeechPlaying] = useState(false);
  const [speechPlayingID, setSpeechPlayingID] = useState('');
  const [played, setPlayed] = useState([]);
  const [hideFooter, setHideFooter] = useState(false);
  const synth = window.speechSynthesis;
  const voices = synth?.getVoices();

  const isAgentAvailable = useMemo(() => {
    if (config?.live_agent_always_active) {
      return true;
    }

    if (!config?.talk_to_live_agent || !config?.live_agent_list_of_schedules?.length) {
      return false;
    }

    try {
      // Get the configured timezone or default to UTC
      const timezone = config?.human_handover_timezone || Intl?.DateTimeFormat()?.resolvedOptions()?.timeZone || 'Europe/Belgrade';
      
      // Get current date/time in the configured timezone using Luxon
      const now = DateTime.now().setZone(timezone);
      const currentDay = now.weekdayLong;
      const currentTime = now.toFormat('HH:mm');

      const schedule = config.live_agent_list_of_schedules.find(s => s.day === currentDay);
      if (!schedule || !schedule.from || !schedule.to) {
        return false;
      }

      // Convert schedule times to DateTime objects in the configured timezone for comparison
      const scheduleStart = DateTime.fromFormat(schedule.from, 'HH:mm', { zone: timezone });
      const scheduleEnd = DateTime.fromFormat(schedule.to, 'HH:mm', { zone: timezone });
      const currentDateTime = DateTime.fromFormat(currentTime, 'HH:mm', { zone: timezone });

      return currentDateTime >= scheduleStart && currentDateTime <= scheduleEnd;
    } catch (error) {
      console.error('Error checking live agent availability:', error);
      return false;
    }
  }, [config?.talk_to_live_agent, config?.live_agent_list_of_schedules, config?.human_handover_timezone, config?.live_agent_always_active]);

  const [dynamicButtons, setDynamicButtons] = useState({
    show_calendly_url: config?.show_calendly_at_the_start || false,
    show_data_form: config?.show_data_collection_on_conversation_begin || false,
    show_live_agent:
      config?.live_agent_always_active ||
      config?.show_live_agent_on_conversation_begin ||
      (config?.talk_to_live_agent && isAgentAvailable) ||
      false,
  });

  const [isLeadGenMode, setIsLeadGenMode] = useState(
    config?.data_collection
      ? config?.show_data_collection_on_conversation_begin
      : false
  );
  const [leadGenInputs, setLeadGenInputs] = useState({
    input_id: null,
    question: null,
  });
  const [savedQuestion, setSavedQuestion] = useState(null);
  const [firstQuestion, setFirstQuestion] = useState(true);
  const [showConsent, setShowConsent] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { handleTask } = useTaskHandler();
  const [chatImageLoader, setChatImageLoader] = useState(false);
  const [hasMic, setHasMic] = useState(config?.has_mic);

  const addConversation = usePreviousConversationsStore(
    (state) => state.addConversation
  );

  const [sources, setSources] = useState([]);
  const [openSources, setOpenSources] = useState(false);
  const [showSources, setShowSources] = useState([]);

  const hasLoadedMessages = useRef(false);

  const [messagesLoading, setMessagesLoading] = useState(false);
  const [showVoicePreview, setShowVoicePreview] = useState(false);
  const [isPreviousConversationHasLiveAgent, setIsPreviousConversationHasLiveAgent] = useState(false);

  /**
   * Creates a new conversation and returns its data.
   * @returns {Object|null} The conversation data or null if an error occurs.
   */
  const createConversation = useCallback(async () => {
    try {
      const res = await conversationsService.postCreateConversation({
        kb_id: config?.kb_id,
      });
      return res?.data || {};
    } catch (error) {
      console.error('Error creating conversation:', error);
      return null;
    }
  }, [config?.id]);

  /**
   * Creates a new conversation for embeds (bubble, direct link, and embed) and returns its data.
   * @returns {Object|null} The conversation data or null if an error occurs.
   */
  const createSharedConversation = useCallback(async () => {
    try {
      const res = await conversationsService.postSharedCreateConversation({
        kb_id: config?.kb_id,
        token: config?.token,
      });
      return res.data;
    } catch (error) {
      console.error('Error creating conversation:', error);
      return null;
    }
  }, [config?.id]);

    /**
   * Scrolls the chat to the latest message.
   */
    const scrollMessage = useCallback(() => {
      const chatContainer = chatContainerRef?.current;
      if (chatContainer) {
        chatContainer.scrollTo({
          top: chatContainer.scrollHeight,
          behavior: 'smooth',
        });
      }
    }, []);


  const handlePromptSuggestions = useCallback((suggestions) => {
    setSuggestionPrompts(suggestions);
    setShowSuggestionPrompts(true);
    scrollMessage();
  }, [setSuggestionPrompts, setShowSuggestionPrompts, scrollMessage]);

  /**
   * Handles the display logic for suggested prompts based on configuration
   * @param {boolean} showPromptMessagesOnce - Whether to show prompts only once
   * @param {Function} suggestionCallback - Callback to execute when suggestions should be shown
   * @returns {boolean} - Whether the suggestions should be shown
   */
  const handleSuggestedPromptsDisplay = useCallback((showPromptMessagesOnce, suggestionCallback) => {
    // If we don't need to show prompts only once, always show them
    if (!showPromptMessagesOnce) {
      suggestionCallback();
      return true;
    }

    // Check if we've already shown prompts
    const hasShownPrompts = localStorage.getItem('show_prompt_messages');
    if (!hasShownPrompts) {
      // First time showing prompts
      suggestionCallback();
      localStorage.setItem('show_prompt_messages', 'true');
      return true;
    }

    // Already shown prompts before
    return false;
  }, []);

  /**
   * Requests new suggestions based on the filtered payload.
   *
   * @param {string} currectConversationId - The current conversation ID
   */
  const requestNewSuggestions = useCallback(
    async (currectConversationId) => {
      try {
        const showPromptMessagesOnce = customizationData?.show_prompt_messages;

        const handleSuggestions = async () => {
          const response = await suggestionService.postSuggestions({
            kb_id: config?.kb_id,
            conversation_id: currectConversationId,
          });

          if (response.status === 200) {
            handlePromptSuggestions(response.data.result);
          }
        };

        handleSuggestedPromptsDisplay(showPromptMessagesOnce, handleSuggestions);
      } catch (error) {
        console.error('Error requesting new suggestions:', error);
      }
    },
    [config?.kb_id, customizationData?.show_prompt_messages, handlePromptSuggestions, handleSuggestedPromptsDisplay]
  );

  const createNewConversation = useCallback(async () => {
    try {
      const newConversation = isInApp
        ? await createConversation()
        : await createSharedConversation();

      if (newConversation) {
        setCurrentConversation({ ...newConversation, type: 'new' });
        return newConversation;
      }
    } catch (error) {
      console.error('Error creating conversation:', error);
    }
  }, [createConversation, createSharedConversation, isInApp]);

  /**
   * Finalizes the process of generating an answer and requests new suggestions.
   *
   * This function is called when the process of generating an answer is complete.
   * It closes the query stream and triggers a request for new suggestions based on the current messages.
   */
  const finishGeneratingAnswer = useCallback(
    (currectConversationId) => {
      queryStreamOpen.current = false;
      if(!customizationData?.show_always_same_prompts) {
        requestNewSuggestions(currectConversationId);
      }else{
        handlePromptSuggestions(
          customizationData?.prompt_suggestions || config?.prompt_suggestions || []
        );
      }
      setIsAnswerLoading(false);
    },
    [messages, requestNewSuggestions]
  );

  const handleDynamicLeadGen = async (userAnswer, conversationId) => {
    const res = await conversationsService.getDynamicLeadGen({
      question: leadGenInputs.question || null,
      answer: userAnswer || null,
      input_id: leadGenInputs.input_id,
      stop_after: config?.data_collection?.length,
      conversation_id: conversationId,
      kb_id: config?.kb_id,
    });
    if (res.status === 200) {
      if (res.data.finished) {
        setIsLeadGenMode(false);
        setShowButtons(true);
        return false;
      } else {
        handleMessagesOnChat('add', {
          id: uuidv4(),
          content: res.data.next_question,
          type: 'message',
          role: 'assistant',
        });
        return res.data;
      }
    }
    return null;
  };

  const evaluateRules = async (message, conversationId, type) => {
    try {
      const response = await showDynamicButtons(
        message,
        conversationId,
        urlParams.get('token')
        // {
        //   show_live_agent: connectedToLiveAgent,
        // }
      );
      const data = response.data;

      if (type === 'data_collection') {
        setDynamicButtons((prev) => ({
          ...prev,
          show_data_form: config?.data_collection && data.show_data_form,
        }));
      }

      if (type === 'live_agent') {
        setDynamicButtons((prev) => ({
          ...prev,
          show_live_agent: (config?.talk_to_live_agent && data.show_live_agent),
        }));
      }

      if (type === 'calendly') {
        setDynamicButtons((prev) => ({
          ...prev,
          show_calendly_url:
            config?.calendly_integration_enabled && data.show_calendly_url,
        }));
      }

      if (data.show_data_form && !isLeadGenMode && !isInApp) {
        setIsLeadGenMode(true);
        if (firstQuestion && config?.is_data_collection_optional) {
          setShowConsent(true);
          setShowSuggestionPrompts(false);
          handleMessagesOnChat('add', {
            id: uuidv4(),
            content:
              config?.data_collection_consent_text ||
              DEFAULT_CHAT_MESSAGES.CONSENT_MESSAGE,
            type: 'message',
            role: 'assistant',
          });
          setSavedQuestion({
            id: uuidv4(),
            content: message,
            type: 'message',
            role: 'user',
          });
          setFirstQuestion(false);
          return true;
        }
        const res = await handleDynamicLeadGen(
          firstQuestion ? null : message,
          conversationId
        );
        if (res) {
          setLeadGenInputs({
            input_id: res.next_question_input_id,
            question: res.next_question,
          });
        }
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error evaluating rules:', error);
    }
  };

  const pollAgentTakeover = async (hh_conversation_id) => {
    if (!hh_conversation_id) {
      console.error('Invalid conversation ID for WebSocket connection');
      return;
    }

    try {
      setPollResponse(true);
      setInteractingWithLiveAgent(false);
      setIsAnswerLoading(false);

      // Disconnect any existing connection first
      disconnectWebSocket();

      // Connect to WebSocket
      // This will use the onMessageReceived, onAgentJoined, and onAgentResolved handlers
      // that are already defined in the internalWebSocketHook
      connectWebSocket(hh_conversation_id);
    } catch (e) {
      console.error('Error in pollAgentTakeover:', e);
    }
  };

  //TODO: Refactor this in the future
  const connectToLiveAgent = async (userName, userEmail, conversation) => {
    try {
      const formInputs = [
        {
          input_name: 'name',
          input_type: 'text',
          input_label: 'name',
          input_placeholder: 'name',
          is_mandatory: true,
          input_value: userName,
        },
        {
          input_name: 'email',
          input_type: 'email',
          input_label: 'email',
          input_placeholder: 'email',
          is_mandatory: true,
          input_value: userEmail,
        },
      ];
      await createSharedFormData(
        conversation.id,
        urlParams.get('token'),
        formInputs
      );
      const infoTypeMessage = {
        id: uuidv4(),
        content: 'Connecting you with a team member',
        type: 'info',
      };
      handleMessagesOnChat('add', infoTypeMessage);
      const response = await requestTakeover(
        conversation.id,
        urlParams.get('token'),
        {
          user_email: userEmail,
          user_name: userName,
        }
      );
      await pollAgentTakeover(response.data.id);
    } catch (error) {
      console.error('Error connecting to live agent:', error);
    }
  };

  /**
   * Sends a question to the server and manages the chat state during the process.
   * @param {Object} question - The question object containing content and metadata.
   * @param {Array} imageUrls - Optional array of image URLs to include with the message.
   */
  const sendQuestion = useCallback(
    async (tempQuestion, imageUrls = []) => {
      setIsAnswerLoading(true);
      let isNewConversation = false;
      let sourcesFetching = false;
      const controller = new AbortController();
      setControl(controller);

      let tempAnswer = '';
      let question = tempQuestion;
      let tempAnswerId = uuidv4();

      let conversation = isInHumanHandoverApp ? liveAgentConversation : currentConversation;

      if (!conversation?.id) {
        const newConversation = await createNewConversation();
        if (newConversation) {
          conversation = newConversation;
          isNewConversation = true;
        }
      }
      if (isLeadGenMode && !isInApp) {
        setHideFooter(true);
        setShowButtons(false);
        setIsAnswerLoading(false);
        if (tempQuestion.content === 'no') {
          setIsLeadGenMode(false);
          setShowButtons(true);
          question = savedQuestion;
        } else {
          const res = await handleDynamicLeadGen(
            question.content,
            conversation.id
          );
          if (res) {
            setLeadGenInputs({
              input_id: res.next_question_input_id,
              question: res.next_question,
            });
            return;
          } else {
            question = savedQuestion;
            setShowButtons(true);
          }
        }
      }

      if(config.data_collection && !config?.show_data_collection_on_conversation_begin && !isInApp && !isPreviewMode) {
        const res = await evaluateRules(question.content, conversation.id, 'data_collection');
        if (res) {
          return;
        }
      }

      if (
        config?.talk_to_live_agent &&
        !config?.show_live_agent_on_conversation_begin &&
        (isAgentAvailable || config.show_live_agent_rule !== '') &&
        !isInApp &&
        !isPreviewMode
      ) {
        const res = await evaluateRules(
          question.content,
          conversation.id,
          'live_agent'
        );
        if (res) {
          return;
        }
      }

      if(config?.calendly_integration_enabled && !config?.show_calendly_at_the_start && !isInApp && !isPreviewMode) {
        const res = await evaluateRules(question.content, conversation.id, 'calendly');
        if (res) {
          return;
        }
      }

      if (interactingWithLiveAgent) {
        setShowButtons(false);
        if (!userName) {
          setUserName(question.content); // Save the name
          setIsAnswerLoading(false);
          // Ask for the email
          const liveAgentEmailRequestMessage = {
            id: uuidv4(),
            content: `Thank you, ${question.content}. ${
              config?.live_agent_email_prompt ||
              'Now, could you please provide your email?'
            }`,
            type: 'message',
            role: 'assistant',
          };
          handleMessagesOnChat('add', liveAgentEmailRequestMessage);
        } else {
          setUserEmail(question.content);
          connectToLiveAgent(userName, question.content, conversation);
        }
        return;
      }

      if (pollResponse && !isLiveAgentResolved) {
        setIsAnswerLoading(false);

        // Send message through WebSocket
        sendWebSocketMessage(
          question.content,
          questionIsQuickResponse,
          imageUrls || []
        );

        setQuestionIsQuickResponse(false);
        return;
      }

      setHideFooter(false);
      handleMessagesOnChat('add', {
        id_temp: tempAnswerId,
        content: '',
        type: 'normal',
        role: 'assistant',
        status: 'loading',
        reaction: '',
        date_created: new Date(),
      });

      // setSuggestionPrompts([]);
      setShowSuggestionPrompts(false);

      const handleError = (err) => {
        // notify(
        //   'failure',
        //   `${
        //     embedMode
        //       ? 'We could not process your request. Please try again and if the issue persists, contact support for help.'
        //       : 'We could not process your request. Please try again and if the issue persists, contact <NAME_EMAIL> for help.'
        //   }`
        // );
        if (err instanceof FatalError) {
          throw err;
        } else {
          controller.abort();
          setIsAnswerLoading(false);
          throw new RetriableError();
        }
      };

      const handleMessage = (event) => {
        queryStreamOpen.current = true;

        if (event.event === 'error') {
          // handle error here...
        }

        if (event.event === 'ping' || event.event === 'talk') return;

        if (event.event === 'message_id') {
          handleMessagesOnChat('update', {
            id: event.data,
            id_temp: tempAnswerId,
          });
          setShowSources([...showSources, event.data]);
          return;
        }

        if (event.data === '_LOADING_IMAGE_START_ ') {
          setChatImageLoader(true);
        } else if (event.data === '_LOADING_IMAGE_END_') {
          setChatImageLoader(false);
        } else if (event.data === '_STREAMING_FINISHED_') {
          setSourcesReady(true);
          controller.abort();
          const answer = {
            id_temp: tempAnswerId,
            content: tempAnswer,
            status: 'complete',
            role: 'assistant',
            reaction: '',
          };
          handleMessagesOnChat('update', answer);
          finishGeneratingAnswer(conversation.id);
          setMessageLoaded(true);
        } else if (event.data === '_STARTING_SOURCES_FETCHING_') {
          console.log('tempAnswerId', tempAnswerId);
        } else if (!sourcesFetching) {
          const parsedData = formatEventData(event.data);
          tempAnswer += parsedData;
          handleMessagesOnChat('update', {
            id_temp: tempAnswerId,
            content: tempAnswer,
            status: 'typing',
            role: 'assistant',
            reaction: '',
          });
        }
      };

      const handleOpenStream = (res) => {
        if (res.status === 426) {
          setSaveSignUpBtn(true);
          window.parent.postMessage(
            {
              eventType: 'signup-btn',
              eventData: true,
            },
            '*'
          );
        }
        if (res.ok && res.status === 200) {
        } else if (
          res.status >= 400 &&
          res.status < 500 &&
          res.status !== 429
        ) {
          if (res.status === 422) {
            // notify('failure', 'Could not process your request.');
          }
          if (res.status === 402) {
            // notify('failure', 'Monthly credit limit exceeded. Top up using credits top-up button.');
          } else if (res.status === 403) {
            // notify('failure', 'Upgrade your plan to use GPT-4.');
          } else if (res.status === 400) {
            // notify('failure', 'Retrain your untrained chatbot.');
          }
        }
      };
      const handleCloseStream = () => {
        control.abort();
      };

      const messageHandlers = {
        onMessage: handleMessage,
        onOpen: handleOpenStream,
        onClose: handleCloseStream,
        onError: handleError,
      };

      try {
        setMessageLoaded(false);
        setOnGoingStream(true);
        // Use selectedChatbot.llmModel first, then fall back to last_model_used, then fall back to default
        const llmModel = selectedChatbot?.llmModel?.value || selectedChatbot?.last_model_used?.value || LLM_MODEL_DEFAULT.value;
        const conversationPayload = {
          ...conversation,
          llmModel,
          question: question.content,
          messages: messages.slice(-5),
        };

        if (isInApp && !isPreviewMode) {
          await sendMessageInApp({
            conversation: conversationPayload,
            accessToken: config?.access_token,
            ...messageHandlers,
            control: controller,
          });
          const creditsResponse = await userService.getUserCredits();
          if (creditsResponse.status === 200) {
            setUserData({
              ...userData,
              credits_key: {
                ...creditsResponse.data,
              },
            });
          }
        } else if (!isInApp && !isPreviewMode) {
          await sendMessageEmbed({
            conversation: conversationPayload,
            accessToken: config?.token,
            ...messageHandlers,
            control: controller,
            cookie: cookie,
          });
        }
      } catch (error) {
        console.error('Error sending message:', error);
        setOnGoingStream(false);
      }
    },
    [
      currentConversation,
      isInApp,
      config,
      messages,
      setCurrentConversation,
      interactingWithLiveAgent,
      isLeadGenMode,
      questionIsQuickResponse,
      savedQuestion,
      cookie,
    ]
  );
  /**
   * Handles adding or updating messages in the chat.
   *
   * @param {string} action - The action to perform ('add' or 'update').
   * @param {Object} message - The message object to add or update.
   */
  const handleMessagesOnChat = useCallback(
    (action, message) => {
      if (action === 'add') {
        setMessages((prevMessages) => {
          const updatedMessages = [...prevMessages, message];
          return updatedMessages;
        });
      }
      if (action === 'update') {
        setMessages((prevMessages) => {
          const updatedMessages = prevMessages.map((msg) => {
            if (msg.id && msg.id === message.id) {
              return { ...msg, ...message };
            }
            if (msg.id_temp && msg.id_temp === message.id_temp) {
              return { ...msg, ...message };
            }
            return msg;
          });
          return updatedMessages;
        });
      }
      setTimeout(() => {
        scrollMessage();
      }, 500);
    },
    [messages, scrollMessage]
  );

  /**
   * Handles sending a new question.
   *
   * @param {string} question - The question to send to the assistant.
   */
  const handleSendQuestion = useCallback(
    async (question, imageUrls = []) => {
      const questionId = uuidv4();
      const dateCreated = new Date();
      const newQuestion = {
        id: questionId,
        content: question,
        role: isInHumanHandoverApp ? 'assistant' : 'user',
        date_created: dateCreated,
        images: imageUrls,
        type:
          isInHumanHandoverApp && questionIsQuickResponse
            ? 'quick_response'
            : 'message',

      };
      if(isInHumanHandoverApp) {
        newQuestion.agent_name = liveAgentData?.name;
        newQuestion.agent_profile_pic = liveAgentData?.image;
      }
      handleMessagesOnChat('add', newQuestion);
      if (
        isLeadGenMode &&
        firstQuestion &&
        !isInApp
      ) {
        setIsAnswerLoading(false);
        setIsLeadGenMode(true);
        setSavedQuestion(newQuestion);
        setFirstQuestion(false);
        setShowButtons(false);
        setShowSuggestionPrompts(false);
        if(config?.is_data_collection_optional){
          setHideFooter(true);
          setShowConsent(true);
          handleMessagesOnChat('add', {
            id: uuidv4(),
            content:
              config?.data_collection_consent_text ||
              DEFAULT_CHAT_MESSAGES.CONSENT_MESSAGE,
            type: 'message',
            role: 'assistant',
          });
          return;
        }
      }

      await sendQuestion(newQuestion, imageUrls);
    },
    [isLeadGenMode, sendQuestion, firstQuestion, isInApp]
  );


  const handleInitialMessages = useCallback((initialMessages) => {
    setMessages(initialMessages);
    scrollMessage();
  }, []);

  const handleNotification = ({ type, message, duration }) => {
    setNotification({
      show: true,
      type,
      message,
    });

    if (duration) {
      setTimeout(() => {
        setNotification({
          show: false,
        });
      }, duration);
    }
  };

  const resetConversation = useCallback(() => {
    setMessages(
      config?.initial_messages || customizationData?.initial_messages || []
    );
    handlePromptSuggestions(
      customizationData?.prompt_suggestions || config?.prompt_suggestions || []
    );
    setPlayed([]);

    // Reset the message loading flag
    hasLoadedMessages.current = false;

    // Disconnect the WebSocket
    disconnectWebSocket();

  }, [isPreviewMode, config, customizationData, disconnectWebSocket]);

  const handleCloseChat = () => {
    resetCurrentConversation();
    setMessages([]);
    setSuggestionPrompts([]);
    setShouldFetchCustomization(true);
    hasLoadedMessages.current = false;
    handleCloseButton && handleCloseButton();
  };

  /**
   * Loads and processes messages for a specific conversation by its ID.
   * Supports both standard and live agent conversations.
   *
   * @param {string} conversation_id - The ID of the conversation to load messages from.
   * @param {boolean} isLiveAgent - Determines whether to fetch messages from live agent service.
   * @returns {Promise<void>} A promise that resolves when the messages are loaded and processed.
   */
  const loadConversationMessages = async (
    conversation_id,
    isLiveAgent = false
  ) => {
    try {
      // Set loading state to true
      setMessagesLoading(true);

      // For normal loading process (not human handover app), load initial messages immediately
      if (!isInHumanHandoverApp && config?.initial_messages?.length > 0 && !isLiveAgent) {
        // Get initial messages and transform them into proper format
        const initialMessages = (config.initial_messages || []).map(
          (message) => ({
            id: uuidv4(),
            content: message.content,
            role: 'assistant',
            type: 'message',
            status: 'completed',
          })
        );

        // Set these first to display immediately
        if (initialMessages.length > 0) {
          setMessages(initialMessages);
          // Scroll after setting initial messages
          setTimeout(() => {
            scrollMessage();
          }, 100);
        }
      }

      // Fetch the actual conversation messages
      const messagesFetchFn = isLiveAgent
        ? humanHandoverService.getMessagesByConversationId
        : isInApp
        ? messagesService.getMessagesByConversationId
        : messagesService.getSharedMessagesByConversationId;

      const { data: conversation } = await messagesFetchFn(
        conversation_id,
        !isInApp && !isLiveAgent ? config?.token : undefined,
        timezone,
      );

      if(!isLiveAgent && !isInApp) {
        const { data: conversationMode } = await humanHandoverService.getConversationMode(conversation_id, config?.token);
        if (conversationMode.conversation_mode === 'human_handover') {
          setIsPreviousConversationHasLiveAgent(true);
          pollAgentTakeover(conversationMode.hh_conversation_id);
        }
      }

      // Choose the appropriate transformation function
      const transformFn = isLiveAgent
        ? transformMessageToBubbleMessageHumanHandover
        : transformMessageToBubbleMessage;

      // Transform the conversation results
      const conversationMessages = conversation.results.flatMap(transformFn);

      // Create a helper function to filter out duplicate messages
      const filterDuplicateMessages = (existingMessages, newMessages) => {
        // If we don't have existing messages, just return all new messages
        if (!existingMessages.length) return newMessages;

        // Create a set of existing message IDs for quick lookup
        const existingIds = new Set(existingMessages.map(msg => msg.id));

        // Create a map of content to detect duplicates even if IDs are different
        const contentMap = new Map();
        existingMessages.forEach(msg => {
          if (msg.content) {
            contentMap.set(msg.content, true);
          }
        });

        // Filter out messages that already exist in the current messages
        // Either by ID or by having the same content
        return newMessages.filter(msg => {
          // Skip if ID already exists
          if (existingIds.has(msg.id)) return false;

          // Skip if content already exists (for non-empty content)
          if (msg.content && contentMap.has(msg.content)) return false;

          return true;
        });
      };

      if (isInHumanHandoverApp && isLiveAgent) {
        setMessages(prevMessages => {
          // Check if we already have messages (including initial messages)
          if (prevMessages.length === 0) {
            // Create initial messages
            const initialMessages = (config?.initial_messages || []).map(
              (message) => ({
                id: uuidv4(),
                content: message.content,
                role: 'assistant',
                type: 'message',
                status: 'completed',
              })
            );

            // Filter out any duplicate messages
            const filteredConversationMessages = filterDuplicateMessages(initialMessages, conversationMessages);

            return [
              ...initialMessages,
              ...filteredConversationMessages,
            ];
          } else if (config?.initial_messages?.length > 0 &&
                    prevMessages.length === config.initial_messages.length) {
            const filteredNewMessages = filterDuplicateMessages(prevMessages, conversationMessages);
            return [...prevMessages, ...filteredNewMessages];
          } else {
            const filteredNewMessages = filterDuplicateMessages(prevMessages, conversationMessages);
            return [...prevMessages, ...filteredNewMessages];
          }
        });
      } else {
        // For regular chat, combine initial messages with conversation results
        // Create initial messages
        const initialMessages = (config?.initial_messages || []).map(
          (message) => ({
            id: uuidv4(),
            content: message.content,
            role: 'assistant',
            type: 'message',
            status: 'completed',
          })
        );

        // Filter out any duplicate messages
        const filteredConversationMessages = filterDuplicateMessages(initialMessages, conversationMessages);

        const processedMessages = [
          ...initialMessages,
          ...filteredConversationMessages,
        ];

        // Set the complete set of messages
        setMessages(processedMessages);
      }

      if (!isLiveAgent) {
        setIsLeadGenMode(false);
        setShowConsent(false);
        setShowSources(conversation.results.map((result) => result.id));
      }

      // Final scroll after all messages are loaded
      setTimeout(() => {
        scrollMessage();
        setMessagesLoading(false);
      }, 300);
    } catch (error) {
      console.error('Error loading conversation messages:', error);
      setMessagesLoading(false);
    }
  };

  // Handle Live Agent
  const handleOpenLiveAgent = async () => {
    setShowButtons(false);
    setInteractingWithLiveAgent(true);
    setIsAnswerLoading(false);

    //if there is no conversation id create one
    if (!currentConversation?.id) {
      await createNewConversation();
    }


    const liveAgentNameRequestMessage = {
      id: uuidv4(),
      content:
        config?.live_agent_name_prompt ||
        'Before we connect you to with a member of our team, could you please tell me your name?',
      type: 'message',
      role: 'assistant',
      reaction: '',
    };
    handleMessagesOnChat('add', liveAgentNameRequestMessage);
  };

  const handleUpdateReaction = async ({ message_id, action }) => {
    try {
      if (!message_id) {
        throw new Error('Invalid message_id for updating reaction');
      }

      await messagesService.updateReaction(message_id, action);

      const message = messages.find((msg) => msg.id === message_id);

      if (action) {
        handleNotification({
          type: action === 'thumbs_up' ? 'positive' : 'negative',
          message:
            action === 'thumbs_up'
              ? 'Response rated as helpful'
              : 'Response rated as not helpful',
          duration: 3000,
        });
      } else {
        handleNotification({
          type: 'neutral',
          message: 'Reaction removed',
          duration: 3000,
        });
      }

      handleMessagesOnChat('update', {
        ...message,
        reaction: action,
      });
    } catch (error) {
      console.error(
        `Error updating reaction for message_id=${message_id}, action=${action}:`,
        error
      );
    }
  };

  const ttsPlayHandler = async (index, playAudioFn, token = null) => {
    if (played.includes(index)) return;

    try {
      const { data } = token ? await playAudioFn(token) : await playAudioFn();
      if (!data) {
        setPlayed((prev) => [...prev, index]);
      }
    } catch (error) {
      console.error('Error playing audio TTS:', error);
    }
  };

  const ttsPlay = (index) => ttsPlayHandler(index, ttsService.playAudioTTS);

  const ttsPlayShared = (index, token) =>
    ttsPlayHandler(index, ttsService.playSharedAudioTTS, token);

  const handleSpeech = (answer, index, message) => {
    const synth = window.speechSynthesis;

    if (speechPlaying && speechPlayingID !== index) {
      synth.cancel();
    }

    if (!speechPlaying || (speechPlaying && speechPlayingID !== index)) {
      const voice = voices.find(
        (v) =>
          (v.lang === 'en-US' || v.lang === 'en-GB') &&
          v.name === 'Google UK English Female'
      );
      const speed = 1.05;
      const msg = new SpeechSynthesisUtterance(answer);
      msg.voice = voice || null;
      msg.rate = speed;

      const buffer = setInterval(() => {
        synth.pause();
        synth.resume();
      }, 5000);

      setSpeechPlaying(true);
      setSpeechPlayingID(index);
      let speakDuration;
      msg.onstart = () => {
        setNotification({
          show: true,
          type: 'alert',
          message: 'Reading message',
        });
      };
      msg.onend = (event) => {
        setSpeechPlaying(false);
        setSpeechPlayingID(null);
        clearInterval(buffer);
        setNotification({
          show: false,
        });
        speakDuration = event.elapsedTime / speed;
      };

      synth.speak(msg);

      if (!isInApp) {
        ttsPlayShared(index, config?.token);
      } else {
        ttsPlay(index);
      }
    } else {
      setSpeechPlaying(false);
      setSpeechPlayingID(null);
      synth.cancel();
      setNotification({
        show: false,
      });
      handleMessagesOnChat('update', {
        ...message,
        reaction: 'stop_message',
      });
    }
  };

  const handlePlayMessage = ({ message_id }) => {
    if (!message_id) {
      console.error('Missing message_id for playing message');
      return;
    }

    const message = messages.find((msg) => msg.id === message_id);
    if (message) {
      handleMessagesOnChat('update', {
        ...message,
        reaction: 'play_message',
      });
      handleSpeech(message.content, message.id, message);
    }
  };

  const handleSources = async ({ message_id }) => {
    setSourcesLoading([...sourcesLoading, message_id]);
    try {
      const response = await modelService.getSources(message_id);

      if (response.status === 200) {
        setSources(response.data);
        setOpenSources(true);
        setSourcesLoading([]);
      }
    } catch (error) {
      console.error('Error fetching sources:', error);
    }
  };

  const handleFooterButton = ({ message_id, action }) => {
    if (!message_id) {
      console.error('Invalid parameters for footer button action');
      return;
    }

    const actionHandlers = {
      thumbs_up: () => handleUpdateReaction({ message_id, action }),
      thumbs_down: () => handleUpdateReaction({ message_id, action }),
      play_message: () => handlePlayMessage({ message_id }),
      stop_message: () => handlePlayMessage({ message_id }),
      sources: () => handleSources({ message_id }),
      no_reaction: () =>
        handleUpdateReaction({ message_id, action: 'no_reaction' }),
    };

    const handler = actionHandlers[action];
    if (handler) {
      handler();
    } else {
      console.warn(`Unhandled action: ${action}`);
    }
  };

  useEffect(() => {
    // Only fetch data if the message is loaded and it's not in app
    // is necessary to get the id of the message to update the reaction
    if (!isInApp && messageLoaded) {
      const fetchData = async () => {
        try {
          const responseMessages =
            await messagesService.getSharedMessagesByConversationId(
              currentConversation.id,
              !isInApp && config?.token
            );

          if (responseMessages?.data) {
            const data = responseMessages.data;
            const results = data.results;
            // if the conversation is not in the store, add it
            // if the conversation is in the store, update it with the last message
            addConversation(config?.kb_id, {
              id: currentConversation.id,
              dateCreated: currentConversation.date_created,
              lastMessage: results[results.length - 1].answer,
            });
          }
        } catch (error) {
          console.error('Error fetching data:', error);
        }
      };

      fetchData();
    }
  }, [messageLoaded]);

  useEffect(() => {
    if (shouldFetchCustomization && config?.kb_id && !isPreviewMode && (!customizationData?.id && isInHumanHandoverApp)) {
      // Call the API only when shouldFetch is true
      const fetchCustomization = async () => {
        try {
          const customizationFetchFn = isInApp
            ? customizationService.getChatbotCustomizationById
            : customizationService.getSharedChatbotCustomizationById;

          const { data: customizationResponse } = await customizationFetchFn({
            kb_id: config?.kb_id,
            ...(!isInApp && { token: config?.token }),
          });

          // Update state and handle related data
          setCustomizationData(customizationResponse);
          handlePromptSuggestions(customizationResponse?.prompt_suggestions);
          handleInitialMessages(customizationResponse?.initial_messages);
          setShouldFetchCustomization(false);
        } catch (e) {
          console.error('Error fetching customization:', e);
        }
      };
      fetchCustomization();
    }
  }, [
    shouldFetchCustomization,
    isInApp,
    config?.id,
    config?.access_token,
    handlePromptSuggestions,
    isInHumanHandoverApp
  ]);

  useEffect(() => {
    if (currentConversation.type !== 'opening') {
      resetCurrentConversation();
    }
    return () => {
      handleCloseChat();

      // Disconnect the WebSocket
      disconnectWebSocket();
    };
  }, []);

  useEffect(() => {
    if (
      currentConversation.id === null &&
      currentConversation.type === null &&
      customizationData &&
      !isPreviewMode
    ) {
      resetConversation();
    } else if (
      currentConversation.id !== null &&
      currentConversation.type === 'opening'
    ) {
      loadConversationMessages(currentConversation.id);
    }
  }, [currentConversation]);

  useEffect(() => {
    if (
      isInHumanHandoverApp &&
      liveAgentConversation &&
      customizationData &&
      !hasLoadedMessages.current
    ) {
      hasLoadedMessages.current = true;

      // Set loading state to true before starting
      setMessagesLoading(true);

      // First set initial messages instantly if they exist
      if (config?.initial_messages?.length > 0) {
        const initialMessages = config.initial_messages.map(message => ({
          id: uuidv4(),
          content: message.content,
          role: 'assistant',
          type: 'message',
          status: 'completed',
        }));

        setMessages(initialMessages);

        // Give a small delay before scrolling to make sure DOM is updated
        setTimeout(() => {
          scrollMessage();
        }, 100);
      }

      // Then load the conversation messages
      if (liveAgentConversation?.id) {
        loadConversationMessages(liveAgentConversation.id, true);

        // Connect to WebSocket for this conversation
        // This will use the onMessageReceived, onAgentJoined, and onAgentResolved handlers
        // that are already defined in the internalWebSocketHook
        if (liveAgentConversation?.state !== 'resolved') {
          connectWebSocket(liveAgentConversation.id);
        }
      }
    }
  }, [liveAgentConversation, customizationData, connectWebSocket]);

  useEffect(() => {
    setCustomizationData(config);
    setDynamicButtons((prev) => ({
      ...prev,
      show_calendly_url:
        config?.show_calendly_at_the_start &&
        config?.calendly_integration_enabled,
      show_data_form:
        config?.show_data_collection_on_conversation_begin &&
        config?.data_collection,
      show_live_agent:
        config?.talk_to_live_agent && (
          config?.live_agent_always_active ||
          (config?.show_live_agent_on_conversation_begin && isAgentAvailable)
        ),
    }));
    setIsLeadGenMode(
      config?.data_collection
        ? config?.show_data_collection_on_conversation_begin
        : false
    );
    setHasMic(config?.has_mic);
    setShowSuggestionPrompts(config?.suggested_prompts_enabled);
    handlePromptSuggestions(config?.prompt_suggestions);
    if (config?.notification) {
      setNotification(config?.notification);
    }
  }, [config, isAgentAvailable]);

  useEffect(() => {
    setMessages(
      config?.messages
        ? [...config.messages, config.initial_messages]
        : config?.initial_messages || []
    );
  }, [config?.initial_messages, config?.messages]);

  useEffect(() => {
    if (showSuggestionPrompts) {
      scrollMessage();
    }
  }, [showSuggestionPrompts]);

  useEffect(() => {
    if (
      liveAgentConversation &&
      liveAgentConversation?.state !== 'waiting' &&
      liveAgentConversation?.state !== 'ai' &&
      liveAgentConversation?.state !== 'resolved'
    ) {
      pollAgentTakeover(liveAgentConversation.id);
    }
  }, [liveAgentConversation]);

  useEffect(() => {
    if (promptToSend) {
      setTimeout(() => {
        localStorage.setItem('show_prompt_messages', 'true');
        handleSendQuestion(promptToSend)
          .then(() => {
            onPromptProcessed();
          })
          .catch((error) => {
            console.error('Error sending prompt:', error);
          });
      }, 1000);
    }
  }, [promptToSend, onPromptProcessed]);

  useEffect(() => {
    setIsLiveAgentResolved(isLiveAgentConversationResolved);
  }, [isLiveAgentConversationResolved]);

  const humanHandoverTakeConversation = async () => {
    try {
      if (!liveAgentConversation?.id) {
        console.error('Cannot take conversation: Missing conversation ID');
        return;
      }

      const response = await humanHandoverService.takeConversation(
        liveAgentConversation.id
      );

      if (response.status === 200) {
        addOpenedConversation({
          ...response.data,
          open: true,
        });

        // Connect to the WebSocket if not already connected
        // Use pollAgentTakeover to handle the WebSocket connection
        // This will use the existing WebSocket hook with all its handlers
        await pollAgentTakeover(liveAgentConversation.id);

        if (isInHumanHandoverApp) {
          setTimeout(() => {
            setLiveAgentConversationEvent(liveAgentConversation.id, {
              type: 'human_handover_taken',
              conversation_id: liveAgentConversation.id,
            });
          }, 100);
        }
      }
    } catch (error) {
      console.error('Error taking conversation:', error);
      setNotification({
        show: true,
        type: 'error',
        message: 'Failed to take conversation. Please try again.',
      });
    }
  };

  const humanHandoverMarkResolved = async () => {
    try {
      if (!liveAgentConversation?.id) {
        console.error('Cannot resolve conversation: Missing conversation ID');
        return;
      }

      const response = await humanHandoverService.markResolved(
        liveAgentConversation.id
      );

      if (response.status === 200) {
        setIsLiveAgentResolved(true);
        setPollResponse(false);
        addOpenedConversation({
          ...response.data,
          open: false,
        });

        // Send the human_handover_resolved message - only if NOT in the human handover app
        if (isInHumanHandoverApp) {
          sendWebSocketControlMessage('human_handover_resolved', liveAgentConversation.id);
        }

        // Disconnect the WebSocket
        disconnectWebSocket();
      }
    } catch (error) {
      console.error('Error marking conversation as resolved:', error);
      setNotification({
        show: true,
        type: 'error',
        message: 'Failed to mark conversation as resolved. Please try again.',
      });
    }
  };

  // Function to handle showing the voice preview
  const handleShowVoice = async () => {
    // If there's no current conversation, create a new one
    if (!currentConversation?.id && !isPreviewMode) {
      try {
        const newConversation = await createNewConversation();
        if (!newConversation) {
          console.error('Failed to create a new conversation for voice preview');
          // We could show an error message to the user here
          return;
        }
        // Wait a moment for the conversation to be properly set up
        await new Promise(resolve => setTimeout(resolve, 100));
      } catch (error) {
        console.error('Error creating conversation for voice preview:', error);
        return;
      }
    }

    // Show the voice preview
    setShowVoicePreview(true);
  };

  // Function to handle closing the voice preview
  const handleCloseVoice = () => {
    setShowVoicePreview(false);

    // Add a delay to allow AI Voice messages to be processed in the backend
    // before refreshing the chat history
    if (currentConversation?.id) {
      // Set loading state immediately to show loader during delay
      setMessagesLoading(true);

      setTimeout(() => {
        loadConversationMessages(currentConversation.id);
      }, 2500); // 2.5 second delay to ensure messages are loaded in backend
    }
  };

  useEffect(() => {
    // Cleanup function to close WebSocket connection when component unmounts
    return () => {
      // Disconnect the WebSocket
      disconnectWebSocket();
    };
  }, [disconnectWebSocket]);

  useEffect(() => {
    if (isInHumanHandoverApp && isWebSocketConnected) {
      const liveAgentConversationEvents = getLiveAgentConversationEvents(liveAgentConversation.id);
      if (liveAgentConversationEvents) {
        liveAgentConversationEvents.forEach(event => {
          sendWebSocketControlMessage(event.type, event.conversation_id);
          deleteLiveAgentConversationEvent(liveAgentConversation.id, event);
        });
      }
    }
  }, [isInHumanHandoverApp, isWebSocketConnected, sendEventConnected]);

  useEffect(() => {
    const unlockAudio = () => {
      unlockAudioContext();
      document.removeEventListener('click', unlockAudio);
    };
    document.addEventListener('click', unlockAudio, { once: true });
  }, []);

  return (
    <>
      {/* Voice Preview Modal */}
      {showVoicePreview && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-[#000000] bg-opacity-80">
          <div className="relative w-full h-full sm:max-w-[90%] md:max-w-4xl lg:max-w-4xl sm:max-h-[85vh] md:max-h-[90vh] sm:rounded-lg overflow-hidden">
            {/* External close button for mobile - only visible on small screens */}
            <button
              className="absolute top-2 right-2 sm:hidden z-10 p-2 bg-black bg-opacity-50 rounded-full text-white hover:bg-opacity-70 transition-all"
              onClick={handleCloseVoice}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
            <div className="w-full h-full">
              <VoicePreview
                chatbotId={customizationData?.kb_id}
                voiceId={!isPreviewMode ? customizationData?.ai_voice_agent_id : 'a56a7938-bd6b-44b4-8e52-2c652946d528'}
                welcomeMessage={!isPreviewMode ? customizationData?.initial_messages.map(message => message.content).join(' ') : `I'm your AI Voice Assistant, fully trained on your chatbot data. Ask me anything connected to your chatbot data, and I'll provide the answers. Let's begin! ${customizationData?.initial_messages?.map(msg => msg.content).join(' ')}`}
                personalityPrompt={!isPreviewMode ? customizationData?.personality_prompt : ''}
                authToken={isInApp ? auth?.access_token : !isPreviewMode ? urlParams.get('token') : ''}
                conversationId={!isPreviewMode ? currentConversation?.id : ''}
                onClose={handleCloseVoice}
                endpointType={isInApp || isPreviewMode ? WS_ENDPOINT_TYPES.BROWSER : WS_ENDPOINT_TYPES.SHARED}
              />
            </div>
          </div>
        </div>
      )}

      <Chat
        config={{
          ...customizationData,
          prompt_suggestions: suggestionPrompts,
          messages: messages,
          notification,
        }}
        chatContainerRef={chatContainerRef}
        handleSendQuestion={handleSendQuestion}
        hiddenConversation={hiddenConversation}
        showInAppHeader={showInAppHeader}
        showMenuBtn={showMenuBtn}
        hiddenPoweredByDante={
          isDanteFaq || isInApp ? true : customizationData?.remove_watermark
        }
        hideFooter={hideFooter}
        handleOpenDanteConversations={handleOpenDanteConversations}
        handleOpenDanteFaq={handleOpenDanteFaq}
        isDanteFaq={isDanteFaq}
        showCloseBtn={showCloseBtn}
        handleCloseButton={handleCloseChat}
        isAnswerLoading={isAnswerLoading}
        isInApp={isInApp}
        isPreviewMode={isPreviewMode}
        handleOpenLiveAgent={handleOpenLiveAgent}
        showButtons={showButtons}
        dynamicButtons={dynamicButtons}
        setCookie={setCookie}
        handleFooterButton={handleFooterButton}
        showConsent={showConsent}
        setShowConsent={setShowConsent}
        hasMic={config?.has_mic}
        showSuggestionPrompts={showSuggestionPrompts}
        sources={sources}
        openSources={openSources}
        setOpenSources={setOpenSources}
        showSources={showSources}
        sourcesLoading={sourcesLoading}
        chatImageLoader={chatImageLoader}
        isInHumanHandoverApp={isInHumanHandoverApp}
        humanHandoverConfig={{
          isTaken:
            liveAgentConversation?.state !== 'waiting' &&
            liveAgentConversation?.state !== 'ai',
          isFlagged: liveAgentConversation?.is_flagged,
        }}
        humanHandoverTakeConversation={humanHandoverTakeConversation}
        humanHandoverMarkResolved={humanHandoverMarkResolved}
        setQuestionIsQuickResponse={setQuestionIsQuickResponse}
        interactingWithLiveAgent={interactingWithLiveAgent}
        isLiveAgentResolved={isLiveAgentResolved}
        setIsLiveAgentResolved={setIsLiveAgentResolved}
        pollResponse={pollResponse}
        messagesLoading={messagesLoading}
        handleShowVoice={handleShowVoice}
        isPreviousConversationHasLiveAgent={isPreviousConversationHasLiveAgent}
        showEmailTranscript={showEmailTranscript}
        setShowEmailTranscript={setShowEmailTranscript}
        currectConversationId={currentConversation?.id}
        setNotification={setNotification}
      />
    </>
  );
};

ChatManager.propTypes = {
  config: PropTypes.shape({
    initial_messages: PropTypes.array,
    messages: PropTypes.array,
    has_mic: PropTypes.bool,
    show_calendly_at_the_start: PropTypes.bool,
    calendly_integration_enabled: PropTypes.bool,
    show_data_collection_on_conversation_begin: PropTypes.bool,
    data_collection: PropTypes.bool,
    show_live_agent_on_conversation_begin: PropTypes.bool,
    talk_to_live_agent: PropTypes.bool,
    notification: PropTypes.object,
    kb_id: PropTypes.string,
    token: PropTypes.string,
    prompt_suggestions: PropTypes.array,
    suggested_prompts_enabled: PropTypes.bool,
    data_collection_consent_text: PropTypes.string,
    remove_watermark: PropTypes.bool,
  }),
  hiddenConversation: PropTypes.bool,
  showInAppHeader: PropTypes.bool,
  showMenuBtn: PropTypes.bool,
  isInApp: PropTypes.bool,
  isDanteFaq: PropTypes.bool,
  showCloseBtn: PropTypes.bool,
  handleOpenDanteConversations: PropTypes.func,
  handleOpenDanteFaq: PropTypes.func,
  handleCloseButton: PropTypes.func,
  hiddenLlmSelector: PropTypes.bool,
  initialShouldFetchCustomization: PropTypes.bool,
  isPreviewMode: PropTypes.bool,
  cookie: PropTypes.string,
  setCookie: PropTypes.func,
  forceRefetch: PropTypes.bool,
  sources: PropTypes.array,
  openSources: PropTypes.bool,
  setOpenSources: PropTypes.func,
  promptToSend: PropTypes.string,
  onPromptProcessed: PropTypes.func,
  webSocketHook: PropTypes.object,
};

export default ChatManager;
