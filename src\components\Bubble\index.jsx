import React, { useEffect, useState, useRef } from 'react';
import Cha<PERSON><PERSON>anager from '../Chatbot/ChatManager';
import DButtonIcon from '../Global/DButtonIcon';
import ChevronLeftIcon from '../Global/Icons/ChevronLeftIcon';
import CloseIcon from '../Global/Icons/CloseIcon';
import OptionsIcon from '../Global/Icons/OptionsIcon';
import HomeContent from './HomeContent';
import DashboardIcon from '../Global/Icons/DashboardIcon';
import AiChatbotIcon from '../Global/Icons/AiChatbotIcon';
import DFullLogo from '../Global/DLogo/DFullLogo';
import ResetIcon from '../Global/Icons/ResetIcon';
import UpRightIcon from '../Global/Icons/UpRightIcon';
import DInput from '../Global/DInput/DInput';
import DButton from '../Global/DButton';
import VoiceUI from '../Voice/VoiceUI';
import * as chatbotService from '../../services/chatbot.service';
import DChatPassword from '../DChatPassword';
import DModalEmailTransaction from '../DModalEmailTransaction';
import { useConversationStore } from '@/stores/conversation/conversationStore';
import BubblePage from '@/pages/BubblePage';
import BubblePopup from '../BubblePopup';
import EmailIcon from '../Global/Icons/EmailIcon';
import DShapeLogo from '../Global/DLogo/DShapeLogo';

const Bubble = ({
  config,
  type = 'chatbot',
  initialShouldFetchCustomization,
  isInApp = false,
  isPreviewMode = false,
  isVoiceMode = false,
  slots,
}) => {
  const [activeTab, setActiveTab] = useState(config?.initialActiveTab || 'home');
  const [hideLogo, setHideLogo] = useState(false);
  const [showOptions, setShowOptions] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(!config?.public);
  const [cookie, setCookie] = useState(null); //this is used on bubble when password is enabled
  const bubbleRef = useRef(null);
  const [showEmailTranscript, setShowEmailTranscript] = useState(false);
  const [logoOpacity, setLogoOpacity] = useState(1);
  const [promptToSend, setPromptToSend] = useState(null);
  const [orderedTabs, setOrderedTabs] = useState([]);

  const resetCurrentConversation = useConversationStore(
    (state) => state.resetCurrentConversation
  );

  const handleTabChange = (tab) => {
    setActiveTab(tab);
    config?.onTabChange?.(tab);
  };

  const handleScroll = () => {
    if (bubbleRef.current) {
      const scrollTop = bubbleRef.current.scrollTop;
      const newOpacity = Math.max(1 - scrollTop / 70, 0);
      setLogoOpacity(newOpacity);
    }
  };

  const resetChat = () => {
    resetCurrentConversation();
  };
  const handleCloseClick = () => {
    const message = { eventType: 'chatbotCloseClick', eventData: true };
    if (config.custom_url) {
      window.parent.parent.postMessage(message, '*');
    } else {
      window.parent.postMessage(message, '*');
    }
  };

  useEffect(() => {
    setShowPasswordModal(!config?.public);
  }, [config?.public]);

  useEffect(() => {
    const currentRef = bubbleRef.current;
    if (currentRef) {
      currentRef.addEventListener('scroll', handleScroll);
    }
    return () => {
      if (currentRef) currentRef.removeEventListener('scroll', handleScroll);
    };
  }, []);

  useEffect(() => {
    const message = { eventType: 'siteReady' };

    // if (window.parent !== window.top) {
    //   window.top.postMessage(message, '*');
    // } else {
    window.parent.postMessage(message, '*');
    // }
  }, []);

  useEffect(() => {
    const handleMessage = (event) => {
      if (event.data.eventType === 'promptClick') {
        setPromptToSend(event.data.eventData);
        setActiveTab('chat');
      }
    };

    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);

  useEffect(() => {
    if (config?.initialActiveTab && config.initialActiveTab !== activeTab) {
      setActiveTab(config.initialActiveTab);
    }
  }, [config?.initialActiveTab]);

  useEffect(() => {
    const tabs = [
      {
        id: 'home',
        order: config?.main_tabs?.home?.order || 1,
        label: config?.main_tabs?.home?.label || 'Home',
        icon: <DashboardIcon className="font-bold" />,
        onClick: () => {
          handleTabChange('home');
          setShowOptions(false);
        },
      },
      {
        id: 'chat',
        order: config?.main_tabs?.chat?.order || 2,
        label: config?.main_tabs?.chat?.label || 'Chat',
        icon: <AiChatbotIcon className="font-bold" />,
        onClick: () => handleTabChange('chat'),
      },
    ];

    const sorted = [...tabs].sort((a, b) => a.order - b.order);
    setOrderedTabs(sorted);
  }, [config?.main_tabs]);

  return !isVoiceMode ? (
    <>
      <div
        className="flex flex-col rounded-size1 h-full grow bubble relative no-scrollbar relative overflow-hidden"
        style={{
          background:
            activeTab === 'home' && config?.home_tab_enabled
              ? 'radial-gradient(243.51% 165.3% at -5% -5%, var(--dt-color-brand-60) 0%, var(--dt-color-surface-100) 40%)'
              : 'var(--dt-color-surface-100)',
          // borderColor: `var(--dt-color-element-5)`,
          // borderWidth: '1px',
        }}
      >
        <header className="p-size3 absolute top-0 left-0 right-0">
          {activeTab === 'home' && config?.home_tab_enabled ? (
            <div className="flex items-center justify-between w-full">
              {!hideLogo && config?.embed_logo_url ? (
                <img
                  src={
                    config?.embed_logo_url instanceof File
                      ? URL.createObjectURL(config?.embed_logo_url)
                      : config?.embed_logo_url
                  }
                  style={{
                    opacity: logoOpacity,
                    transition: 'opacity 0.3s ease',
                  }}
                  className="max-w-[150px] max-h-[28px] w-auto h-auto"
                />
              ) : (
                <div
                  className="max-w-[150px] max-h-[20px]"
                  style={{
                    opacity: logoOpacity,
                    transition: 'opacity 0.3s ease',
                  }}
                >
                  <DFullLogo />
                </div>
              )}
              <DButtonIcon
                variant="squared"
                className="size-8 backdrop-blur-lg ml-auto !z-10"
                style={{
                  color: 'var(--dt-color-element-100)',
                  borderColor: 'var(--dt-color-element-2)',
                  backgroundColor: 'var(--dt-color-element-2)',
                }}
                onClick={handleCloseClick}
              >
                <CloseIcon />
              </DButtonIcon>
            </div>
          ) : (
            <div className="flex items-center justify-between">
              {/* <DButtonIcon
                variant="squared"
                style={{
                  color: 'var(--dt-color-element-100)',
                  borderColor: 'var(--dt-color-element-5)',
                  backgroundColor: 'var(--dt-color-surface-100)',
                }}
                className={`px-size0 !z-10 ${
                  !config?.home_tab_enabled ? 'hidden' : ''
                }`}
                disabled={isPreviewMode}
                onClick={() => {
                  handleTabChange('home');
                  setShowOptions(false);
                }}
              >
                <ChevronLeftIcon />
              </DButtonIcon> */}
              <div className="flex items-center gap-size0">
              {config?.embed_logo_url && !isInApp && !isPreviewMode ? (
                <img
                  src={
                    config?.embed_logo_url instanceof File
                      ? URL.createObjectURL(config?.embed_logo_url)
                      : config?.embed_logo_url
                  }
                  style={{
                    opacity: logoOpacity,
                    transition: 'opacity 0.3s ease',
                  }}
                  className="max-w-[150px] max-h-[28px] w-auto h-auto"
                />):     
                  <DButtonIcon className="!w-[20px]">
                    <DShapeLogo className="!size-5"/>
                  </DButtonIcon>
                }
                <h2
                  className="text-lg"
                  style={{
                    color: 'var(--dt-color-element-100)',
                  }}
                >
                  {config?.name || config?.kb_name}
              </h2>
              </div>
              <div className="flex items-center gap-size2">
                <DButtonIcon
                  variant="squared"
                  style={{
                    color: 'var(--dt-color-element-100)',
                    borderColor: 'var(--dt-color-element-5)',
                    backgroundColor: 'var(--dt-color-surface-100)',
                  }}
                  className="px-size0 !z-10"
                  disabled={isPreviewMode}
                  onClick={() => setShowOptions(!showOptions)}
                >
                  <OptionsIcon />
                </DButtonIcon>
                {!isInApp && <DButtonIcon
                  variant="squared"
                  className="size-8 backdrop-blur-lg !z-10"
                  disabled={isPreviewMode}
                  style={{
                    color: 'var(--dt-color-element-100)',
                    borderColor: 'var(--dt-color-element-2)',
                    backgroundColor: 'var(--dt-color-element-2)',
                  }}
                  onClick={handleCloseClick}
                >
                  <CloseIcon />
                </DButtonIcon>}
              </div>
            </div>
          )}
        </header>

        <div
          className="bubble-body flex-grow p-4 overflow-auto pt-[64px] no-scrollbar"
          ref={bubbleRef}
        >
          {activeTab === 'home' && config?.home_tab_enabled ? (
            <HomeContent
              kb_id={config?.kb_id}
              token={config?.token}
              slots={slots}
              isPreviewMode={isPreviewMode}
              setActiveTab={handleTabChange}
              hidePoweredByDante={config?.remove_watermark}
              previous_conversation_enabled={
                config?.previous_conversation_enabled
              }
            />
          ) : (
            type === 'chatbot' && (
              <ChatManager
                config={config}
                isInApp={isInApp}
                initialShouldFetchCustomization={
                  initialShouldFetchCustomization
                }
                isPreviewMode={isPreviewMode}
                cookie={cookie}
                setCookie={setCookie}
                showInAppHeader={false}
                promptToSend={promptToSend}
                onPromptProcessed={() => setPromptToSend(null)}
                setShowEmailTranscript={setShowEmailTranscript}
                showEmailTranscript={showEmailTranscript}
              />
            )
          )}
        </div>
        {config?.home_tab_enabled && (
          <footer
            className="flex-none flex h-[76px border-t border-grey-5"
            style={{
              background: `linear-gradient(0deg, var(--dt-color-surface-100), var(--dt-color-surface-100)),
                 linear-gradient(0deg, rgba(9, 8, 31, 0.01), rgba(9, 8, 31, 0.01))`,
            }}
          >
            {orderedTabs.map((tab) => (
              <button
                key={tab.id}
                className={`flex-1 py-3 text-center text-sm font-regular flex flex-col items-center justify-center gap-size0 ${
                  activeTab === tab.id
                    ? 'text-[var(--dt-color-element-100)]'
                    : 'text-[var(--dt-color-element-50)]'
                }`}
                onClick={tab.onClick}
              >
                {/* {tab.icon} */}
                {tab.label}
              </button>
            ))}
          </footer>
        )}
        {/* options menu */}
        <div
          className={`absolute bottom-0 left-0 right-0 py-size3 px-size2 flex flex-col gap-size1 bg-[var(--dt-color-surface-100)] h-[calc(100% - 84px)] justify-end transition-transform duration-300 ease-in-out ${
            showOptions ? 'translate-x-0' : 'translate-x-full'
          }`}
        >
          {config.show_email_details && (
            <button
              className="flex justify-between text-[var(--dt-color-element-100)] items-center gap-size1 py-size1 px-size2 rounded-size1 hover:bg-[var(--dt-color-element-5)]  border-b border-grey-5"
              onClick={() => {
                setShowOptions(false);
                setShowEmailTranscript(true);
              }}
            >
              <p className="text-base  font-regular tracking-tight">
                Email transcript
              </p>
              <EmailIcon />
            </button>
          )}
          <button
            className="flex justify-between text-[var(--dt-color-element-100)] items-center gap-size1 py-size1 px-size2 rounded-size1 hover:bg-[var(--dt-color-element-5)]  border-b border-grey-5"
            onClick={() => {
              setShowOptions(false);
              resetChat();
            }}
          >
            <p className="text-base font-regular tracking-tight">Reset chat</p>
            <ResetIcon />
          </button>
          <button
            className="flex justify-between text-[var(--dt-color-element-100)] items-center gap-size1 py-size1 px-size2 rounded-size1 hover:bg-[var(--dt-color-element-5)]  border-b border-grey-5"
            onClick={() =>
              window.open(
                config?.terms_of_use_link ||
                  'https://www.dante-ai.com/terms-of-service',
                '_blank'
              )
            }
          >
            <p className="text-base font-regular tracking-tight">
              Terms of use
            </p>
            <UpRightIcon />
          </button>
          <button
            className="flex justify-between text-[var(--dt-color-element-100)] items-center gap-size1 py-size1 px-size2 rounded-size1 hover:bg-[var(--dt-color-element-5)]"
            onClick={() =>
              window.open(
                config?.privacy_policy_link ||
                  'https://www.dante-ai.com/privacy-policy',
                '_blank'
              )
            }
          >
            <p className="text-base font-regular tracking-tight">
              Privacy policy
            </p>
            <UpRightIcon />
          </button>
        </div>
        {!config?.public && showPasswordModal && (
          <div className="absolute bottom-0 left-0 right-0 h-full flex flex-col gap-size1 backdrop-blur-sm justify-end">
            <div className="bg-white p-size5 rounded-size1 flex flex-col gap-size1 z-1">
              <DChatPassword
                kb_id={config?.kb_id}
                setCookie={setCookie}
                onUnlockSuccess={() => setShowPasswordModal(false)}
                onBubble={true}
              />
            </div>
          </div>
        )}
      </div>
    </>
  ) : (
    <VoiceUI />
  );
};

export default Bubble;
