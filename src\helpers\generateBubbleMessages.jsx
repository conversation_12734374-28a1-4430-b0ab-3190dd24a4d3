import React from 'react';
import DOMPurify from 'dompurify';
import { BlockMath, InlineMath } from 'react-katex';
import ReactMarkdown from 'react-markdown';
import { dark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import rehypeKatex from 'rehype-katex';
import rehypeRaw from 'rehype-raw';
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import rehypeExternalLinks from 'rehype-external-links';
import 'katex/dist/katex.min.css';
import DCopyBtn from '@/components/Global/DCopyBtn';
// import './style.css';

const SyntaxHighlighter = React.lazy(() =>
  import('react-syntax-highlighter/dist/esm/prism')
);

const Pre = ({ children }) => (
  <pre className="blog-pre">
    <DCopyBtn>{children}</DCopyBtn>
    {children}
  </pre>
);

const OrderList = ({ children, start }) => (
  <ol
    className="ml-[15px] list-outside list-decimal space-y-0 !mt-1"
    style={{ color: 'var(--dt-color-element-75)' }}
    start={start}
  >
    {children}
  </ol>
);
const UnorderedList = ({ children }) => (
  <ul className="ml-[20px] list-outside list-disc space-y-0 !mt-1">
    {children}
  </ul>
);
const ListItem = ({ children }) => <li className="!my-2" style={{ fontSize: 'var(--dt-font-size)' }} >{children}</li>;
const Paragraph = ({ children }) => (
  <p
    className="peer peer:mt-size3 leading-[20px] [&:not(:last-child)]:mb-size1"
    style={{ fontSize: 'var(--dt-font-size)' }}
  >
    {children}
  </p>
);
const Link = ({ children, href, target, kb_id, role, ...props }) => (
  <a
    className={`${(kb_id === 'ca7ac107-9716-4ef3-a6b5-55ceab49f15a' || kb_id === '86927718-7690-4c0c-a99d-8bc8afda0a4c') && role === 'user' ? '!text-[#fff]' : '!text-[var(--dt-color-brand-100)]'}`}
    href={href}
    target={target}
    {...props}
  >
    {children}
  </a>
);

const HorizontalRule = () => <hr className="my-4 border-t border-gray-300" />;

const TableRenderer = ({ children }) => {
  return (
    <div className="overflow-x-auto my-4">
      <table className="border-collapse table-auto w-full text-sm">
        {children}
      </table>
    </div>
  );
};

const TableHeader = ({ children }) => {
  return (
    <th className="border-b border-gray-300 p-size1 text-left">{children}</th>
  );
};

const TableCell = ({ children }) => {
  return (
    <td className="border-b border-gray-300 p-size1 text-left">{children}</td>
  );
};

const ImageRenderer = ({ src, alt }) => {
  return <img src={src} alt={alt} className="custom-markdown-image" />;
};

const handleMathTags = (content) => {
  const latexCommands = [
    // Functions
    'int', 'sum', 'prod', 'lim', 'log', 'ln', 'exp', 'sin', 'cos', 'tan', 'cot', 'sec', 'csc',
    'arcsin', 'arccos', 'arctan', 'max', 'min', 'sup', 'inf', 'deg', 'det', 'dim', 'gcd',
    'hom', 'ker', 'Pr', 'mod',
  
    // Operators & Symbols
    'cdot', 'times', 'div', 'pm', 'mp', 'frac', 'sqrt', 'leq', 'geq', 'neq', 'approx', 'asymp',
    'equiv', 'propto', 'infty', 'partial', 'nabla', 'rightarrow', 'leftarrow', 'leftrightarrow',
    'Rightarrow', 'Leftarrow', 'Leftrightarrow', 'to', 'mapsto', 'longrightarrow',
    'longleftarrow', 'hookrightarrow', 'hookleftarrow', 'uparrow', 'downarrow', 'updownarrow',
  
    // Logic & Sets
    'land', 'lor', 'neg', 'lnot', 'in', 'notin', 'ni', 'subset', 'supset', 'subseteq', 'supseteq',
    'cup', 'cap', 'setminus', 'emptyset', 'forall', 'exists', 'therefore', 'because',
    'vdash', 'dashv', 'models',
  
    // Greek Letters (lowercase and uppercase)
    'alpha', 'beta', 'gamma', 'delta', 'epsilon', 'varepsilon', 'zeta', 'eta', 'theta', 'vartheta',
    'iota', 'kappa', 'lambda', 'mu', 'nu', 'xi', 'pi', 'varpi', 'rho', 'varrho', 'sigma',
    'varsigma', 'tau', 'upsilon', 'phi', 'varphi', 'chi', 'psi', 'omega',
    'Gamma', 'Delta', 'Theta', 'Lambda', 'Xi', 'Pi', 'Sigma', 'Upsilon', 'Phi', 'Psi', 'Omega',
  
    // Accents
    'hat', 'bar', 'tilde', 'vec', 'dot', 'ddot', 'check', 'breve', 'acute', 'grave',
    'overline', 'underline',
  
    // Fonts and Text
    'text', 'mathrm', 'mathcal', 'mathbb', 'mathbf', 'mathit', 'mathtt', 'mathsf', 'boldsymbol',
  
    // Absolute value and delimiters
    'lvert', 'rvert', 'lVert', 'rVert', 'vert', 'Vert', 'lbrack', 'rbrack', 'langle', 'rangle',
    'lceil', 'rceil', 'lfloor', 'rfloor', 'left', 'right',
  
    // Brackets and Delimiter Shorthands
    'big', 'Big', 'bigg', 'Bigg', 'bigl', 'bigr', 'Bigl', 'Bigr',
  
    // Other
    'boxed', 'cancel', 'binom', 'tbinom', 'dbinom', 'choose', 'stackrel', 'overset', 'underset',
    'not', 'phantom', 'hphantom', 'vphantom', 'dots', 'cdots', 'ldots', 'vdots', 'ddots',
    'quad', 'qquad', 'tag', ',', '.', ':', ';', '!', '%'
  ];
  
  
  const mathTags = ['dante_math', 'dante_formula'];
  let fixedContent = content;

  for (const tag of mathTags) {
    if (fixedContent.includes(`<${tag}>`)) {
      const mathRegex = new RegExp(`<${tag}>(.*?)</${tag}>`, 'gs');
      let match;
      let tempContent = fixedContent;

      while ((match = mathRegex.exec(fixedContent)) !== null) {
        let mathContent = match[1];

        latexCommands.forEach((cmd) => {
          const pattern = new RegExp(`"${cmd}\\b`, 'g');
          mathContent = mathContent.replace(pattern, `\\${cmd}`);
        });

        mathContent = mathContent.replace(/"([^a-zA-Z])/g, '$1');

        mathContent = mathContent.replace(/"/g, '');

        mathContent = mathContent.replace(/\$/g, '');

        tempContent = tempContent.replace(match[0], `$$$${mathContent}$$$`);
      }
      fixedContent = tempContent;
    }
  }
  fixedContent = fixedContent.replaceAll('__DOLLAR_SIGN__', '\\text{\\$}');
  return fixedContent;
};

const cleanDanteImageTags = (content) => {
  if (!content.includes('<dante_image>')) return content;

  const imageRegex = /<dante_image>(.*?)<\/dante_image>/gs;
  let match;
  let tempContent = content;

  while ((match = imageRegex.exec(content)) !== null) {
    let imageContent = match[1].trim();
    console.log('match', match);
    console.log('imageContent', imageContent);
    tempContent = tempContent.replace(match[0], imageContent);
  }

  return tempContent;
};

const cleanDanteUrlTags = (content) => {
  if (!content.includes('<dante_url>')) return content;

  const urlRegex = /<dante_url>(.*?)<\/dante_url>/gs;
  let match;
  let tempContent = content;

  while ((match = urlRegex.exec(content)) !== null) {
    let urlContent = match[1].trim();
    tempContent = tempContent.replace(match[0], urlContent);
  }

  return tempContent;
};

const sanitizeContent = (content, isUser) => {
  if (!content) return '';

  let fixedContent = content;

  // Handle dante math tags
  fixedContent = handleMathTags(fixedContent);

  // Handle dante_table tags before other processing
  if (fixedContent.includes('<dante_table>')) {
    const parts = fixedContent.split('<dante_table>');
    let result = parts[0]; // Keep content before the table

    for (let i = 1; i < parts.length; i++) {
      const tablePart = parts[i];
      if (tablePart.includes('</dante_table>')) {
        // Split at closing tag and keep both table content and remaining content
        const [tableContent, remainingContent] =
          tablePart.split('</dante_table>');
        result += tableContent.trim() + (remainingContent || '');
      } else {
        // If no closing tag, keep the content as is
        result += tablePart;
      }
    }
    fixedContent = result;
  }

  if (fixedContent.includes('<dante_image>')) {
    fixedContent = cleanDanteImageTags(fixedContent);
  }

  if (fixedContent.includes('<dante_url>')) {
    fixedContent = cleanDanteUrlTags(fixedContent);
  }

  // Preserve horizontal rules by replacing them with a special marker
  fixedContent = fixedContent
    .replace(/^\s*-{3,}\s*$/gm, '___HORIZONTAL_RULE___')
    .trim();

  // Split by newlines, preserve empty lines for spacing
  const lines = fixedContent.split('\n');

  // Process lines to maintain proper spacing
  const processedLines = [];
  let previousLineEmpty = false;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const trimmedLine = line.trim();

    // Handle horizontal rule markers
    if (trimmedLine === '___HORIZONTAL_RULE___') {
      processedLines.push('---');
      continue;
    }

    // If this is an empty line
    if (trimmedLine.length === 0) {
      // Only add one empty line (avoid multiple consecutive empty lines)
      if (!previousLineEmpty) {
        processedLines.push('');
        previousLineEmpty = true;
      }
    } else {
      // This is a non-empty line
      processedLines.push(line);
      previousLineEmpty = false;
    }
  }

  // Join the processed lines back together
  fixedContent = processedLines.join('\n');

  // Use DOMPurify only for user-generated content
  if (isUser) {
    fixedContent = DOMPurify.sanitize(fixedContent);
  }

  // Escape isolated dollar signs that aren't part of a math expression
  // fixedContent = fixedContent.replace(/([^\\])\$(?!\d)/g, '$1\\$');
  return fixedContent;
};

const generateMarkdownBubble = (urlTransform, ele, kb_id) => {
  const isUser = ele?.role === 'user';
  const content = sanitizeContent(ele?.content, isUser);

  return (
    <ReactMarkdown
      remarkPlugins={[remarkGfm, [remarkMath, { singleDollarTextMath: false }]]}
      rehypePlugins={[
        rehypeRaw,
        rehypeKatex,
        [rehypeExternalLinks, { target: '_blank', rel: 'noreferrer' }],
      ]}
      urlTransform={urlTransform}
      components={{
        table: TableRenderer,
        th: TableHeader,
        td: TableCell,
        p: Paragraph,
        pre: Pre,
        a: ({ children, href, ...props }) => (
          <Link href={href} kb_id={kb_id} role={ele?.role} {...props}>
            {children}
          </Link>
        ),
        ul: UnorderedList,
        ol: ({ start, children, ...props }) => (
          <OrderList start={start} {...props}>
            {children}
          </OrderList>
        ),
        li: ListItem,
        img: ImageRenderer,
        hr: HorizontalRule,
        code({ node, inline, className, children, ...props }) {
          const match = /language-(\w+)/.exec(className || '');
          return !inline && match ? (
            <SyntaxHighlighter
              {...props}
              style={dark}
              language={match[1]}
              PreTag="div"
              className="custom-syntax-highlighter"
            >
              {String(children).replace(/\n$/, '')}
            </SyntaxHighlighter>
          ) : (
            <code {...props} className={className}>
              {children}
            </code>
          );
        },
        math: ({ value }) => {
          return <BlockMath>{value}</BlockMath>;
        },
        inlineMath: ({ value }) => {
          return <InlineMath>{value}</InlineMath>;
        },
      }}
    >
      {content}
    </ReactMarkdown>
  );
};

export default generateMarkdownBubble;
